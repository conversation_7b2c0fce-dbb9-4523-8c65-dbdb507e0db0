@echo off
echo ========================================
echo PolyBot Simple - Bitcoin Trading Bot
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if requirements are installed
echo Checking dependencies...
python -c "import requests, pandas, numpy, matplotlib" >nul 2>&1
if errorlevel 1 (
    echo Installing required dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Check if config file exists
if not exist "config.ini" (
    echo Warning: config.ini not found
    echo Please make sure to configure your API keys
    echo.
)

echo Starting PolyBot Simple...
echo.
python main.py

echo.
echo Bot execution completed.
pause
