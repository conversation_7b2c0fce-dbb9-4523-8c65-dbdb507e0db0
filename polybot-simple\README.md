# PolyBot Simple - Bitcoin Trading Bot

A simplified Bitcoin trading bot for Polymarket with Gemini API integration, featuring automated backtesting and live trading capabilities.

## Features

- **Market Data Retrieval**: Real-time and historical data from Gemini API
- **Predictive Analysis**: Moving average crossover strategy
- **Risk Management**: Stop-loss and take-profit mechanisms
- **Order Execution**: Integration with Polymarket API
- **Backtesting**: Comprehensive strategy testing with performance metrics
- **Monitoring**: Trade logging and performance tracking
- **Configuration**: Easy setup through config.ini file

## Quick Start

### 1. Installation

```bash
# Clone or download the files to your polybot-simple directory
cd polybot-simple

# Install required dependencies
pip install -r requirements.txt
```

### 2. Configuration Setup

1. Open `config.ini` file
2. Fill in your API credentials:
   ```ini
   [API]
   gemini_api_key = your_gemini_api_key_here
   gemini_api_secret = your_gemini_api_secret_here
   polymarket_api_key = your_polymarket_api_key_here
   polymarket_api_secret = your_polymarket_api_secret_here
   ```
3. Adjust trading parameters to your preferences:
   ```ini
   [TRADING]
   symbol = BTCUSD
   trade_size = 0.001
   short_ma_window = 10
   long_ma_window = 50
   stop_loss_pct = 2.0
   take_profit_pct = 5.0
   ```

### 3. Backtesting

Run the bot to automatically backtest the strategy:

```bash
python main.py
```

The bot will:
- Fetch historical data
- Run backtest simulation
- Display performance metrics
- Show equity curves and trade analysis

### 4. Live Trading

After satisfactory backtesting results:
1. The bot will ask if you want to start live trading
2. Type 'y' to start live trading
3. Monitor performance through the logging system
4. Press Ctrl+C to stop the bot gracefully

## Configuration Parameters

### API Settings
- `gemini_api_key`: Your Gemini API key for market data
- `gemini_api_secret`: Your Gemini API secret
- `polymarket_api_key`: Your Polymarket API key for trading
- `polymarket_api_secret`: Your Polymarket API secret

### Trading Settings
- `symbol`: Trading pair (default: BTCUSD)
- `trade_size`: Amount to trade per signal (default: 0.001)
- `short_ma_window`: Short-term moving average period (default: 10)
- `long_ma_window`: Long-term moving average period (default: 50)
- `stop_loss_pct`: Stop loss percentage (default: 2.0%)
- `take_profit_pct`: Take profit percentage (default: 5.0%)

### Bot Settings
- `update_interval`: How often to check for signals in seconds (default: 60)
- `backtest_days`: Number of days for backtesting (default: 30)
- `log_level`: Logging level (default: INFO)

## Strategy

The bot uses a **Moving Average Crossover Strategy**:
- **Buy Signal**: When short-term MA crosses above long-term MA
- **Sell Signal**: When short-term MA crosses below long-term MA
- **Risk Management**: Automatic stop-loss and take-profit orders

## Performance Metrics

The bot tracks and displays:
- Total Profit/Loss ($ and %)
- Win Rate
- Total number of trades
- Sharpe Ratio
- Maximum Drawdown
- Average Profit/Loss per trade

## File Structure

```
polybot-simple/
├── main.py           # Main trading bot application
├── utils.py          # Utility classes (ConfigManager, Backtester)
├── config.ini        # Configuration file
├── requirements.txt  # Python dependencies
├── README.md         # This file
├── trading_bot.log   # Log file (created when running)
└── trades.db         # SQLite database for trade history (created when running)
```

## Logging

The bot creates detailed logs in:
- **Console**: Real-time status updates
- **trading_bot.log**: Detailed log file with all activities
- **trades.db**: SQLite database with trade history and performance metrics

## Safety Features

- **Paper Trading Mode**: Test strategies without real money
- **Risk Management**: Built-in stop-loss and take-profit
- **Error Handling**: Comprehensive error handling and logging
- **Graceful Shutdown**: Clean exit with Ctrl+C

## API Requirements

### Gemini API
- Sign up at [Gemini](https://www.gemini.com/)
- Create API keys in your account settings
- No special permissions needed for market data

### Polymarket API
- Sign up at [Polymarket](https://polymarket.com/)
- Create API keys for trading
- Ensure sufficient balance for trading

## Troubleshooting

### Common Issues

1. **Import Error**: Make sure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **API Connection Error**: Check your API keys in config.ini

3. **No Data Error**: Verify internet connection and API endpoints

4. **Permission Error**: Ensure API keys have necessary permissions

### Support

For issues or questions:
1. Check the log files for detailed error messages
2. Verify your configuration settings
3. Ensure API keys are valid and have proper permissions

## Disclaimer

This trading bot is for educational and research purposes. Trading cryptocurrencies involves substantial risk of loss. Always test thoroughly with paper trading before using real money. The authors are not responsible for any financial losses.
