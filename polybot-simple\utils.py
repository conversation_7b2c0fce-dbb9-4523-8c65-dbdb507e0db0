"""
Utility classes for the PolyBot Simple trading bot.
Contains ConfigManager, Backtester, and additional helper functions.
"""

import configparser
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from typing import Dict
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    """Class to manage bot configuration."""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        Initialize the configuration manager.
        
        Args:
            config_file: Path to configuration file
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file."""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file)
            else:
                self.create_default_config()
                self.save_config()
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self.create_default_config()
    
    def create_default_config(self) -> None:
        """Create default configuration."""
        self.config["API"] = {
            "gemini_api_key": "",
            "gemini_api_secret": "",
            "polymarket_api_key": "",
            "polymarket_api_secret": ""
        }
        
        self.config["TRADING"] = {
            "symbol": "BTCUSD",
            "trade_size": "0.001",
            "short_ma_window": "10",
            "long_ma_window": "50",
            "stop_loss_pct": "2.0",
            "take_profit_pct": "5.0"
        }
        
        self.config["BOT"] = {
            "update_interval": "60",
            "backtest_days": "30",
            "log_level": "INFO"
        }
    
    def save_config(self) -> None:
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                self.config.write(f)
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    def get(self, section: str, key: str, fallback: str = None) -> str:
        """
        Get configuration value.
        
        Args:
            section: Configuration section
            key: Configuration key
            fallback: Fallback value if key not found
            
        Returns:
            Configuration value
        """
        try:
            return self.config.get(section, key, fallback=fallback)
        except:
            return fallback
    
    def getint(self, section: str, key: str, fallback: int = None) -> int:
        """
        Get configuration value as integer.
        
        Args:
            section: Configuration section
            key: Configuration key
            fallback: Fallback value if key not found
            
        Returns:
            Configuration value as integer
        """
        try:
            return self.config.getint(section, key, fallback=fallback)
        except:
            return fallback
    
    def getfloat(self, section: str, key: str, fallback: float = None) -> float:
        """
        Get configuration value as float.
        
        Args:
            section: Configuration section
            key: Configuration key
            fallback: Fallback value if key not found
            
        Returns:
            Configuration value as float
        """
        try:
            return self.config.getfloat(section, key, fallback=fallback)
        except:
            return fallback
    
    def getboolean(self, section: str, key: str, fallback: bool = None) -> bool:
        """
        Get configuration value as boolean.
        
        Args:
            section: Configuration section
            key: Configuration key
            fallback: Fallback value if key not found
            
        Returns:
            Configuration value as boolean
        """
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except:
            return fallback

class Backtester:
    """Class to backtest trading strategies."""
    
    def __init__(self, data_fetcher, model, strategy, risk_manager):
        """
        Initialize the backtester.
        
        Args:
            data_fetcher: Market data fetcher instance
            model: Predictive model instance
            strategy: Trading strategy instance
            risk_manager: Risk management instance
        """
        self.data_fetcher = data_fetcher
        self.model = model
        self.strategy = strategy
        self.risk_manager = risk_manager
    
    def run_backtest(self, symbol: str, days: int = 30, initial_balance: float = 10000.0) -> Dict:
        """
        Run a backtest for the specified number of days.
        
        Args:
            symbol: Trading symbol
            days: Number of days to backtest
            initial_balance: Initial balance for the backtest
            
        Returns:
            Dictionary with backtest results
        """
        try:
            # Fetch historical data
            logger.info(f"Fetching historical data for {symbol} for {days} days")
            data = self.data_fetcher.get_historical_data(symbol, "1day", days)
            
            if data.empty:
                logger.error("No historical data available for backtesting")
                return {"error": "No historical data available"}
            
            # Fit the model
            logger.info("Fitting predictive model")
            self.model.fit(data)
            
            # Initialize backtest variables
            balance = initial_balance
            position = 0  # Current position (0 for no position, positive for long, negative for short)
            entry_price = 0
            trades = []
            equity_curve = [initial_balance]
            
            # Simulate trading
            logger.info("Running backtest simulation")
            for i in range(self.model.long_window, len(data)):
                current_data = data.iloc[:i+1]
                current_price = current_data["close"].iloc[-1]
                
                # Generate prediction
                prediction = self.model.predict(current_data)
                
                # Generate trading signal
                signal = self.strategy.generate_signal(current_data, prediction)
                
                # Check if we need to close a position due to stop loss or take profit
                if position != 0:
                    stop_loss = self.risk_manager.calculate_stop_loss(entry_price, "buy" if position > 0 else "sell")
                    take_profit = self.risk_manager.calculate_take_profit(entry_price, "buy" if position > 0 else "sell")
                    
                    action = self.risk_manager.check_stop_loss_take_profit(
                        current_price, entry_price, "buy" if position > 0 else "sell", stop_loss, take_profit
                    )
                    
                    if action == "close":
                        # Close position
                        trade_result = balance * (current_price / entry_price - 1) if position > 0 else balance * (1 - current_price / entry_price)
                        balance += trade_result
                        
                        trades.append({
                            "timestamp": current_data["timestamp"].iloc[-1],
                            "action": "sell" if position > 0 else "buy",
                            "price": current_price,
                            "profit_loss": trade_result,
                            "reason": "Stop loss/take profit"
                        })
                        
                        position = 0
                        entry_price = 0
                
                # Open new position if there's a signal and no current position
                if signal["action"] != "hold" and position == 0:
                    position = 1 if signal["action"] == "buy" else -1
                    entry_price = current_price
                    
                    trades.append({
                        "timestamp": current_data["timestamp"].iloc[-1],
                        "action": signal["action"],
                        "price": current_price,
                        "profit_loss": 0,
                        "reason": signal["reason"]
                    })
                
                # Update equity curve
                if position != 0:
                    current_equity = balance * (current_price / entry_price) if position > 0 else balance * (2 - current_price / entry_price)
                else:
                    current_equity = balance
                
                equity_curve.append(current_equity)
            
            # Calculate performance metrics
            total_trades = len([t for t in trades if t["action"] in ["buy", "sell"]])
            winning_trades = sum(1 for t in trades if t["profit_loss"] > 0)
            losing_trades = sum(1 for t in trades if t["profit_loss"] < 0)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            total_profit_loss = balance - initial_balance
            avg_profit_loss = total_profit_loss / total_trades if total_trades > 0 else 0
            
            # Calculate Sharpe ratio (simplified)
            daily_returns = [equity_curve[i+1] / equity_curve[i] - 1 for i in range(len(equity_curve)-1)]
            sharpe_ratio = np.mean(daily_returns) / np.std(daily_returns) * np.sqrt(365) if np.std(daily_returns) > 0 else 0
            
            # Calculate max drawdown
            peak = equity_curve[0]
            max_drawdown = 0
            for value in equity_curve:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            # Prepare results
            results = {
                "initial_balance": initial_balance,
                "final_balance": balance,
                "total_profit_loss": total_profit_loss,
                "total_profit_loss_pct": (total_profit_loss / initial_balance) * 100,
                "total_trades": total_trades,
                "winning_trades": winning_trades,
                "losing_trades": losing_trades,
                "win_rate": win_rate,
                "avg_profit_loss": avg_profit_loss,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "max_drawdown_pct": max_drawdown * 100,
                "trades": trades,
                "equity_curve": equity_curve
            }
            
            logger.info(f"Backtest completed. Total profit/loss: {total_profit_loss:.2f} ({(total_profit_loss / initial_balance) * 100:.2f}%)")
            
            return results
            
        except Exception as e:
            logger.error(f"Error during backtest: {e}")
            return {"error": str(e)}

    def plot_backtest_results(self, results: Dict) -> None:
        """
        Plot backtest results.

        Args:
            results: Backtest results dictionary
        """
        try:
            if "error" in results:
                logger.error(f"Cannot plot results: {results['error']}")
                return

            # Create figure with subplots
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

            # Plot equity curve
            ax1.plot(results["equity_curve"], color='blue')
            ax1.set_title("Equity Curve")
            ax1.set_ylabel("Balance")
            ax1.grid(True)

            # Plot profit/loss per trade
            trades = [t for t in results["trades"] if t["profit_loss"] != 0]
            if trades:
                trade_numbers = range(1, len(trades) + 1)
                profits = [t["profit_loss"] for t in trades]
                colors = ['green' if p > 0 else 'red' for p in profits]

                ax2.bar(trade_numbers, profits, color=colors)
                ax2.set_title("Profit/Loss per Trade")
                ax2.set_xlabel("Trade Number")
                ax2.set_ylabel("Profit/Loss")
                ax2.grid(True)

            # Adjust layout
            plt.tight_layout()
            plt.show()

        except Exception as e:
            logger.error(f"Error plotting backtest results: {e}")
