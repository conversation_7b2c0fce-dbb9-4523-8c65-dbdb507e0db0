# PolyBot Simple Configuration Example
# Copy this file to config.ini and fill in your API keys

[API]
# Gemini API credentials for market data
# Get these from: https://www.gemini.com/
gemini_api_key = your_gemini_api_key_here
gemini_api_secret = your_gemini_api_secret_here

# Polymarket API credentials for trading
# Get these from: https://polymarket.com/
polymarket_api_key = your_polymarket_api_key_here
polymarket_api_secret = your_polymarket_api_secret_here

[TRADING]
# Trading symbol (e.g., BTCUSD, ETHUSD)
symbol = BTCUSD

# Trade size (amount to trade per signal)
# Start small for testing: 0.001 BTC = ~$30-50
trade_size = 0.001

# Moving average windows for strategy
# Short MA crosses Long MA = signal
short_ma_window = 10
long_ma_window = 50

# Risk management parameters
# Stop loss: 2% = exit if price drops 2%
stop_loss_pct = 2.0
# Take profit: 5% = exit if price rises 5%
take_profit_pct = 5.0

[BOT]
# Update interval in seconds (how often to check for signals)
# 60 = check every minute
update_interval = 60

# Number of days for backtesting
# 30 = test strategy on last 30 days of data
backtest_days = 30

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
log_level = INFO
