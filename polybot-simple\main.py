#!/usr/bin/env python3
"""
Bitcoin Trading Bot for Polymarket with Gemini API Integration

This module implements a complete trading bot with the following features:
1. Market Data Retrieval from Gemini API
2. Predictive Analysis using moving averages
3. Trading Strategy based on predictions
4. Risk Management with stop-loss and take-profit
5. Order Execution via Polymarket API
6. Monitoring and Logging of trades
7. User Configuration capabilities
8. Backtesting functionality

Usage Instructions:
1. Configuration Setup: Create a config.ini file with your API keys
2. Backtesting: Run the bot to automatically backtest the strategy
3. Live Trading: Start the bot for live trading after satisfactory backtesting
"""

import requests
import json
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import configparser
import os
import sqlite3
import matplotlib.pyplot as plt
from abc import ABC, abstractmethod

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MarketDataFetcher(ABC):
    """Abstract base class for market data fetchers."""
    
    @abstractmethod
    def get_historical_data(self, symbol: str, period: str, limit: int) -> pd.DataFrame:
        """Fetch historical market data."""
        pass
    
    @abstractmethod
    def get_current_price(self, symbol: str) -> float:
        """Fetch current price."""
        pass
    
    @abstractmethod
    def get_order_book(self, symbol: str) -> Dict:
        """Fetch order book data."""
        pass

class GeminiDataFetcher(MarketDataFetcher):
    """Class to fetch market data from Gemini API."""
    
    def __init__(self, api_key: str = None, api_secret: str = None):
        """
        Initialize the Gemini data fetcher.
        
        Args:
            api_key: Gemini API key
            api_secret: Gemini API secret
        """
        self.base_url = "https://api.gemini.com/v1"
        self.api_key = api_key
        self.api_secret = api_secret
        self.session = requests.Session()
        
        if api_key and api_secret:
            self.session.auth = (api_key, api_secret)
    
    def get_historical_data(self, symbol: str, period: str = "1day", limit: int = 500) -> pd.DataFrame:
        """
        Fetch historical price data from Gemini.
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSD")
            period: Time period for candles ("1min", "5min", "15min", "30min", "1hr", "6hr", "1day")
            limit: Number of candles to fetch
            
        Returns:
            DataFrame with historical price data
        """
        try:
            url = f"{self.base_url}/candles/{symbol}/{period}"
            params = {"limit": limit}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Convert to DataFrame
            df = pd.DataFrame(data, columns=["timestamp", "open", "high", "low", "close", "volume"])
            
            # Convert timestamp to datetime
            df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")
            
            # Convert price columns to float
            for col in ["open", "high", "low", "close", "volume"]:
                df[col] = df[col].astype(float)
            
            # Sort by timestamp
            df = df.sort_values("timestamp")
            
            return df
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching historical data: {e}")
            return pd.DataFrame()
    
    def get_current_price(self, symbol: str) -> float:
        """
        Fetch current price from Gemini.
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSD")
            
        Returns:
            Current price
        """
        try:
            url = f"{self.base_url}/pubticker/{symbol}"
            
            response = self.session.get(url)
            response.raise_for_status()
            
            data = response.json()
            
            return float(data["last"])
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching current price: {e}")
            return 0.0
    
    def get_order_book(self, symbol: str, limit: int = 50) -> Dict:
        """
        Fetch order book from Gemini.
        
        Args:
            symbol: Trading pair symbol (e.g., "BTCUSD")
            limit: Number of bids/asks to fetch
            
        Returns:
            Dictionary with order book data
        """
        try:
            url = f"{self.base_url}/book/{symbol}"
            params = {"limit_bids": limit, "limit_asks": limit}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching order book: {e}")
            return {}

class PredictiveModel(ABC):
    """Abstract base class for predictive models."""
    
    @abstractmethod
    def fit(self, data: pd.DataFrame) -> None:
        """Fit the model to historical data."""
        pass
    
    @abstractmethod
    def predict(self, data: pd.DataFrame) -> int:
        """
        Generate a prediction (1 for buy, -1 for sell, 0 for hold).
        
        Args:
            data: DataFrame with market data
            
        Returns:
            Prediction signal
        """
        pass

class MovingAverageModel(PredictiveModel):
    """Moving average crossover model."""
    
    def __init__(self, short_window: int = 10, long_window: int = 50):
        """
        Initialize the moving average model.
        
        Args:
            short_window: Window for short-term moving average
            long_window: Window for long-term moving average
        """
        self.short_window = short_window
        self.long_window = long_window
        self.short_ma = None
        self.long_ma = None
    
    def fit(self, data: pd.DataFrame) -> None:
        """
        Calculate moving averages based on historical data.
        
        Args:
            data: DataFrame with historical price data
        """
        # Calculate moving averages
        self.short_ma = data["close"].rolling(window=self.short_window).mean()
        self.long_ma = data["close"].rolling(window=self.long_window).mean()
    
    def predict(self, data: pd.DataFrame) -> int:
        """
        Generate a prediction based on moving average crossover.
        
        Args:
            data: DataFrame with market data
            
        Returns:
            Prediction signal (1 for buy, -1 for sell, 0 for hold)
        """
        if len(data) < self.long_window:
            return 0  # Not enough data
        
        # Calculate moving averages
        short_ma = data["close"].rolling(window=self.short_window).mean().iloc[-1]
        long_ma = data["close"].rolling(window=self.long_window).mean().iloc[-1]
        
        # Previous values
        prev_short_ma = data["close"].rolling(window=self.short_window).mean().iloc[-2]
        prev_long_ma = data["close"].rolling(window=self.long_window).mean().iloc[-2]
        
        # Check for crossover
        if prev_short_ma <= prev_long_ma and short_ma > long_ma:
            return 1  # Buy signal
        elif prev_short_ma >= prev_long_ma and short_ma < long_ma:
            return -1  # Sell signal
        else:
            return 0  # Hold signal

class TradingStrategy(ABC):
    """Abstract base class for trading strategies."""

    @abstractmethod
    def generate_signal(self, data: pd.DataFrame, prediction: int) -> Dict:
        """
        Generate a trading signal based on prediction and market data.

        Args:
            data: DataFrame with market data
            prediction: Prediction from the model

        Returns:
            Dictionary with signal details
        """
        pass

class SimpleMovingAverageStrategy(TradingStrategy):
    """Simple trading strategy based on moving average crossover."""

    def __init__(self, risk_manager):
        """
        Initialize the strategy.

        Args:
            risk_manager: Risk management instance
        """
        self.risk_manager = risk_manager

    def generate_signal(self, data: pd.DataFrame, prediction: int) -> Dict:
        """
        Generate a trading signal based on moving average crossover prediction.

        Args:
            data: DataFrame with market data
            prediction: Prediction from the model

        Returns:
            Dictionary with signal details
        """
        current_price = data["close"].iloc[-1]

        signal = {
            "action": "hold",
            "price": current_price,
            "timestamp": data["timestamp"].iloc[-1],
            "reason": "No signal"
        }

        if prediction == 1:  # Buy signal
            signal["action"] = "buy"
            signal["reason"] = "Short-term MA crossed above long-term MA"
        elif prediction == -1:  # Sell signal
            signal["action"] = "sell"
            signal["reason"] = "Short-term MA crossed below long-term MA"

        return signal

class RiskManager:
    """Class to manage trading risks."""

    def __init__(self, stop_loss_pct: float = 2.0, take_profit_pct: float = 5.0):
        """
        Initialize the risk manager.

        Args:
            stop_loss_pct: Stop loss percentage
            take_profit_pct: Take profit percentage
        """
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct

    def calculate_stop_loss(self, entry_price: float, action: str) -> float:
        """
        Calculate stop loss price.

        Args:
            entry_price: Entry price
            action: Trade action ("buy" or "sell")

        Returns:
            Stop loss price
        """
        if action == "buy":
            return entry_price * (1 - self.stop_loss_pct / 100)
        else:  # sell
            return entry_price * (1 + self.stop_loss_pct / 100)

    def calculate_take_profit(self, entry_price: float, action: str) -> float:
        """
        Calculate take profit price.

        Args:
            entry_price: Entry price
            action: Trade action ("buy" or "sell")

        Returns:
            Take profit price
        """
        if action == "buy":
            return entry_price * (1 + self.take_profit_pct / 100)
        else:  # sell
            return entry_price * (1 - self.take_profit_pct / 100)

    def check_stop_loss_take_profit(self, current_price: float, entry_price: float,
                                    action: str, stop_loss: float, take_profit: float) -> str:
        """
        Check if stop loss or take profit has been triggered.

        Args:
            current_price: Current market price
            entry_price: Entry price
            action: Trade action ("buy" or "sell")
            stop_loss: Stop loss price
            take_profit: Take profit price

        Returns:
            Action to take ("close" or "hold")
        """
        if action == "buy":
            if current_price <= stop_loss:
                return "close"  # Stop loss triggered
            elif current_price >= take_profit:
                return "close"  # Take profit triggered
        else:  # sell
            if current_price >= stop_loss:
                return "close"  # Stop loss triggered
            elif current_price <= take_profit:
                return "close"  # Take profit triggered

        return "hold"  # No trigger

class OrderExecutor(ABC):
    """Abstract base class for order executors."""

    @abstractmethod
    def place_order(self, action: str, quantity: float, price: float = None) -> Dict:
        """
        Place an order.

        Args:
            action: Order action ("buy" or "sell")
            quantity: Order quantity
            price: Order price (None for market order)

        Returns:
            Dictionary with order details
        """
        pass

    @abstractmethod
    def get_order_status(self, order_id: str) -> Dict:
        """
        Get order status.

        Args:
            order_id: Order ID

        Returns:
            Dictionary with order status
        """
        pass

    @abstractmethod
    def cancel_order(self, order_id: str) -> Dict:
        """
        Cancel an order.

        Args:
            order_id: Order ID

        Returns:
            Dictionary with cancellation result
        """
        pass

class PolymarketExecutor(OrderExecutor):
    """Class to execute orders on Polymarket."""

    def __init__(self, api_key: str = None, api_secret: str = None):
        """
        Initialize the Polymarket executor.

        Args:
            api_key: Polymarket API key
            api_secret: Polymarket API secret
        """
        self.base_url = "https://api.polymarket.com"
        self.api_key = api_key
        self.api_secret = api_secret
        self.session = requests.Session()

        if api_key and api_secret:
            self.session.headers.update({
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            })

    def place_order(self, action: str, quantity: float, price: float = None) -> Dict:
        """
        Place an order on Polymarket.

        Args:
            action: Order action ("buy" or "sell")
            quantity: Order quantity
            price: Order price (None for market order)

        Returns:
            Dictionary with order details
        """
        try:
            endpoint = "/orders"
            url = f"{self.base_url}{endpoint}"

            order_type = "market" if price is None else "limit"

            payload = {
                "action": action,
                "type": order_type,
                "quantity": quantity,
                "market": "BTC"  # Assuming we're trading Bitcoin
            }

            if price is not None:
                payload["price"] = price

            response = self.session.post(url, json=payload)
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"Error placing order: {e}")
            return {"error": str(e)}

    def get_order_status(self, order_id: str) -> Dict:
        """
        Get order status from Polymarket.

        Args:
            order_id: Order ID

        Returns:
            Dictionary with order status
        """
        try:
            endpoint = f"/orders/{order_id}"
            url = f"{self.base_url}{endpoint}"

            response = self.session.get(url)
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"Error getting order status: {e}")
            return {"error": str(e)}

    def cancel_order(self, order_id: str) -> Dict:
        """
        Cancel an order on Polymarket.

        Args:
            order_id: Order ID

        Returns:
            Dictionary with cancellation result
        """
        try:
            endpoint = f"/orders/{order_id}"
            url = f"{self.base_url}{endpoint}"

            response = self.session.delete(url)
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"Error cancelling order: {e}")
            return {"error": str(e)}

class TradeMonitor:
    """Class to monitor trades and log performance metrics."""

    def __init__(self, db_path: str = "trades.db"):
        """
        Initialize the trade monitor.

        Args:
            db_path: Path to SQLite database for storing trade data
        """
        self.db_path = db_path
        self.init_database()

    def init_database(self) -> None:
        """Initialize the database for storing trade data."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create trades table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    action TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    price REAL NOT NULL,
                    status TEXT NOT NULL,
                    order_id TEXT,
                    entry_price REAL,
                    exit_price REAL,
                    profit_loss REAL,
                    stop_loss REAL,
                    take_profit REAL,
                    reason TEXT
                )
            ''')

            # Create performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    total_trades INTEGER NOT NULL,
                    winning_trades INTEGER NOT NULL,
                    losing_trades INTEGER NOT NULL,
                    win_rate REAL NOT NULL,
                    total_profit_loss REAL NOT NULL,
                    avg_profit_loss REAL NOT NULL
                )
            ''')

            conn.commit()
            conn.close()

        except sqlite3.Error as e:
            logger.error(f"Error initializing database: {e}")

    def log_trade(self, trade_data: Dict) -> None:
        """
        Log a trade to the database.

        Args:
            trade_data: Dictionary with trade details
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO trades (
                    timestamp, action, quantity, price, status, order_id,
                    entry_price, exit_price, profit_loss, stop_loss, take_profit, reason
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_data.get("timestamp", datetime.now().isoformat()),
                trade_data.get("action", ""),
                trade_data.get("quantity", 0),
                trade_data.get("price", 0),
                trade_data.get("status", ""),
                trade_data.get("order_id", ""),
                trade_data.get("entry_price", 0),
                trade_data.get("exit_price", 0),
                trade_data.get("profit_loss", 0),
                trade_data.get("stop_loss", 0),
                trade_data.get("take_profit", 0),
                trade_data.get("reason", "")
            ))

            conn.commit()
            conn.close()

        except sqlite3.Error as e:
            logger.error(f"Error logging trade: {e}")

    def update_performance_metrics(self) -> None:
        """Update performance metrics based on completed trades."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Get completed trades
            cursor.execute('''
                SELECT * FROM trades WHERE status = "completed"
            ''')

            trades = cursor.fetchall()

            if not trades:
                return

            # Calculate metrics
            total_trades = len(trades)
            winning_trades = sum(1 for trade in trades if trade[8] > 0)  # profit_loss > 0
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            total_profit_loss = sum(trade[8] for trade in trades)  # sum of profit_loss
            avg_profit_loss = total_profit_loss / total_trades if total_trades > 0 else 0

            # Insert or update performance metrics for today
            today = datetime.now().strftime("%Y-%m-%d")

            cursor.execute('''
                SELECT * FROM performance WHERE date = ?
            ''', (today,))

            if cursor.fetchone():
                cursor.execute('''
                    UPDATE performance SET
                        total_trades = ?,
                        winning_trades = ?,
                        losing_trades = ?,
                        win_rate = ?,
                        total_profit_loss = ?,
                        avg_profit_loss = ?
                    WHERE date = ?
                ''', (total_trades, winning_trades, losing_trades, win_rate, total_profit_loss, avg_profit_loss, today))
            else:
                cursor.execute('''
                    INSERT INTO performance (
                        date, total_trades, winning_trades, losing_trades,
                        win_rate, total_profit_loss, avg_profit_loss
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (today, total_trades, winning_trades, losing_trades, win_rate, total_profit_loss, avg_profit_loss))

            conn.commit()
            conn.close()

        except sqlite3.Error as e:
            logger.error(f"Error updating performance metrics: {e}")

    def get_performance_metrics(self, days: int = 30) -> pd.DataFrame:
        """
        Get performance metrics for the specified number of days.

        Args:
            days: Number of days to get metrics for

        Returns:
            DataFrame with performance metrics
        """
        try:
            conn = sqlite3.connect(self.db_path)

            query = '''
                SELECT * FROM performance
                WHERE date >= date('now', '-{} days')
                ORDER BY date DESC
            '''.format(days)

            df = pd.read_sql_query(query, conn)
            conn.close()

            return df

        except sqlite3.Error as e:
            logger.error(f"Error getting performance metrics: {e}")
            return pd.DataFrame()

    def plot_performance(self, days: int = 30) -> None:
        """
        Plot performance metrics for the specified number of days.

        Args:
            days: Number of days to plot
        """
        try:
            df = self.get_performance_metrics(days)

            if df.empty:
                logger.warning("No performance data to plot")
                return

            # Create figure with subplots
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15))

            # Plot win rate
            ax1.plot(df["date"], df["win_rate"], marker='o')
            ax1.set_title("Win Rate")
            ax1.set_ylabel("Win Rate")
            ax1.grid(True)

            # Plot total profit/loss
            ax2.plot(df["date"], df["total_profit_loss"], marker='o', color='green')
            ax2.set_title("Total Profit/Loss")
            ax2.set_ylabel("Profit/Loss")
            ax2.grid(True)

            # Plot average profit/loss
            ax3.plot(df["date"], df["avg_profit_loss"], marker='o', color='orange')
            ax3.set_title("Average Profit/Loss")
            ax3.set_ylabel("Avg Profit/Loss")
            ax3.set_xlabel("Date")
            ax3.grid(True)

            # Adjust layout
            plt.tight_layout()
            plt.show()

        except Exception as e:
            logger.error(f"Error plotting performance: {e}")

# Import utility classes
from utils import ConfigManager, Backtester

class TradingBot:
    """Main trading bot class."""

    def __init__(self, config_file: str = "config.ini"):
        """
        Initialize the trading bot.

        Args:
            config_file: Path to configuration file
        """
        # Load configuration
        self.config = ConfigManager(config_file)

        # Set log level
        log_level = self.config.get("BOT", "log_level", "INFO")
        logging.getLogger().setLevel(getattr(logging, log_level))

        # Initialize components
        self.data_fetcher = GeminiDataFetcher(
            api_key=self.config.get("API", "gemini_api_key"),
            api_secret=self.config.get("API", "gemini_api_secret")
        )

        self.model = MovingAverageModel(
            short_window=self.config.getint("TRADING", "short_ma_window", 10),
            long_window=self.config.getint("TRADING", "long_ma_window", 50)
        )

        self.risk_manager = RiskManager(
            stop_loss_pct=self.config.getfloat("TRADING", "stop_loss_pct", 2.0),
            take_profit_pct=self.config.getfloat("TRADING", "take_profit_pct", 5.0)
        )

        self.strategy = SimpleMovingAverageStrategy(self.risk_manager)

        self.executor = PolymarketExecutor(
            api_key=self.config.get("API", "polymarket_api_key"),
            api_secret=self.config.get("API", "polymarket_api_secret")
        )

        self.monitor = TradeMonitor()

        self.backtester = Backtester(
            data_fetcher=self.data_fetcher,
            model=self.model,
            strategy=self.strategy,
            risk_manager=self.risk_manager
        )

        # Trading state
        self.symbol = self.config.get("TRADING", "symbol", "BTCUSD")
        self.trade_size = self.config.getfloat("TRADING", "trade_size", 0.001)
        self.update_interval = self.config.getint("BOT", "update_interval", 60)
        self.is_running = False
        self.current_position = 0  # 0 for no position, 1 for long, -1 for short
        self.entry_price = 0
        self.stop_loss = 0
        self.take_profit = 0

        logger.info("Trading bot initialized")

    def start(self) -> None:
        """Start the trading bot."""
        logger.info("Starting trading bot")
        self.is_running = True

        try:
            while self.is_running:
                self.run_trading_cycle()
                time.sleep(self.update_interval)
        except KeyboardInterrupt:
            logger.info("Trading bot stopped by user")
        except Exception as e:
            logger.error(f"Error in trading bot: {e}")
        finally:
            self.is_running = False
            logger.info("Trading bot stopped")

    def stop(self) -> None:
        """Stop the trading bot."""
        logger.info("Stopping trading bot")
        self.is_running = False

    def run_trading_cycle(self) -> None:
        """Run a single trading cycle."""
        try:
            # Fetch historical data
            data = self.data_fetcher.get_historical_data(self.symbol, "1hr", 100)

            if data.empty:
                logger.warning("No market data available")
                return

            # Fit the model
            self.model.fit(data)

            # Generate prediction
            prediction = self.model.predict(data)

            # Generate trading signal
            signal = self.strategy.generate_signal(data, prediction)

            # Get current price
            current_price = self.data_fetcher.get_current_price(self.symbol)

            # Check if we need to close a position due to stop loss or take profit
            if self.current_position != 0:
                action = self.risk_manager.check_stop_loss_take_profit(
                    current_price, self.entry_price,
                    "buy" if self.current_position > 0 else "sell",
                    self.stop_loss, self.take_profit
                )

                if action == "close":
                    self.close_position(current_price, "Stop loss/take profit")

            # Open new position if there's a signal and no current position
            if signal["action"] != "hold" and self.current_position == 0:
                self.open_position(signal["action"], current_price, signal["reason"])

            logger.info(f"Trading cycle completed. Current position: {self.current_position}, Current price: {current_price}")

        except Exception as e:
            logger.error(f"Error in trading cycle: {e}")

    def open_position(self, action: str, price: float, reason: str) -> None:
        """
        Open a trading position.

        Args:
            action: Action to take ("buy" or "sell")
            price: Current price
            reason: Reason for opening the position
        """
        try:
            logger.info(f"Opening {action} position at price {price}")

            # Place order
            order_result = self.executor.place_order(action, self.trade_size)

            if "error" in order_result:
                logger.error(f"Error placing order: {order_result['error']}")
                return

            # Update position state
            self.current_position = 1 if action == "buy" else -1
            self.entry_price = price
            self.stop_loss = self.risk_manager.calculate_stop_loss(price, action)
            self.take_profit = self.risk_manager.calculate_take_profit(price, action)

            # Log trade
            trade_data = {
                "timestamp": datetime.now().isoformat(),
                "action": action,
                "quantity": self.trade_size,
                "price": price,
                "status": "open",
                "order_id": order_result.get("id", ""),
                "entry_price": price,
                "exit_price": 0,
                "profit_loss": 0,
                "stop_loss": self.stop_loss,
                "take_profit": self.take_profit,
                "reason": reason
            }

            self.monitor.log_trade(trade_data)

            logger.info(f"Opened {action} position at price {price}. Stop loss: {self.stop_loss}, Take profit: {self.take_profit}")

        except Exception as e:
            logger.error(f"Error opening position: {e}")

    def close_position(self, price: float, reason: str) -> None:
        """
        Close the current trading position.

        Args:
            price: Current price
            reason: Reason for closing the position
        """
        try:
            action = "sell" if self.current_position > 0 else "buy"
            logger.info(f"Closing {action} position at price {price}")

            # Place order
            order_result = self.executor.place_order(action, self.trade_size)

            if "error" in order_result:
                logger.error(f"Error placing order: {order_result['error']}")
                return

            # Calculate profit/loss
            profit_loss = self.trade_size * (price - self.entry_price) if self.current_position > 0 else self.trade_size * (self.entry_price - price)

            # Log trade
            trade_data = {
                "timestamp": datetime.now().isoformat(),
                "action": action,
                "quantity": self.trade_size,
                "price": price,
                "status": "completed",
                "order_id": order_result.get("id", ""),
                "entry_price": self.entry_price,
                "exit_price": price,
                "profit_loss": profit_loss,
                "stop_loss": self.stop_loss,
                "take_profit": self.take_profit,
                "reason": reason
            }

            self.monitor.log_trade(trade_data)

            # Update performance metrics
            self.monitor.update_performance_metrics()

            # Reset position state
            self.current_position = 0
            self.entry_price = 0
            self.stop_loss = 0
            self.take_profit = 0

            logger.info(f"Closed {action} position at price {price}. Profit/Loss: {profit_loss}")

        except Exception as e:
            logger.error(f"Error closing position: {e}")

    def run_backtest(self, days: int = None) -> Dict:
        """
        Run a backtest.

        Args:
            days: Number of days to backtest (uses config value if None)

        Returns:
            Dictionary with backtest results
        """
        if days is None:
            days = self.config.getint("BOT", "backtest_days", 30)

        logger.info(f"Running backtest for {days} days")

        results = self.backtester.run_backtest(self.symbol, days)

        if "error" not in results:
            logger.info(f"Backtest completed. Total profit/loss: {results['total_profit_loss']:.2f} ({results['total_profit_loss_pct']:.2f}%)")
            logger.info(f"Win rate: {results['win_rate']:.2%}, Sharpe ratio: {results['sharpe_ratio']:.2f}")
            logger.info(f"Max drawdown: {results['max_drawdown_pct']:.2f}%")

        return results

    def plot_backtest_results(self, days: int = None) -> None:
        """
        Plot backtest results.

        Args:
            days: Number of days to backtest (uses config value if None)
        """
        if days is None:
            days = self.config.getint("BOT", "backtest_days", 30)

        results = self.run_backtest(days)

        if "error" not in results:
            self.backtester.plot_backtest_results(results)

    def get_performance_metrics(self, days: int = 30) -> pd.DataFrame:
        """
        Get performance metrics.

        Args:
            days: Number of days to get metrics for

        Returns:
            DataFrame with performance metrics
        """
        return self.monitor.get_performance_metrics(days)

    def plot_performance(self, days: int = 30) -> None:
        """
        Plot performance metrics.

        Args:
            days: Number of days to plot
        """
        self.monitor.plot_performance(days)

def main():
    """Main function to run the trading bot."""
    print("=" * 60)
    print("PolyBot Simple - Bitcoin Trading Bot")
    print("=" * 60)
    print()

    # Create trading bot
    try:
        bot = TradingBot()
        print("✓ Trading bot initialized successfully")
    except Exception as e:
        print(f"✗ Error initializing trading bot: {e}")
        return

    print()
    print("Configuration Setup:")
    print("- Make sure to fill in your API keys in config.ini")
    print("- Adjust trading parameters to your preferences")
    print()

    # Run backtest
    print("Running backtest...")
    try:
        results = bot.run_backtest()
        if "error" not in results:
            print("✓ Backtest completed successfully")
            print(f"  - Total Profit/Loss: ${results['total_profit_loss']:.2f} ({results['total_profit_loss_pct']:.2f}%)")
            print(f"  - Win Rate: {results['win_rate']:.2%}")
            print(f"  - Total Trades: {results['total_trades']}")
            print(f"  - Sharpe Ratio: {results['sharpe_ratio']:.2f}")
            print(f"  - Max Drawdown: {results['max_drawdown_pct']:.2f}%")

            # Plot results
            try:
                bot.plot_backtest_results()
                print("✓ Backtest charts displayed")
            except Exception as e:
                print(f"✗ Error displaying charts: {e}")
        else:
            print(f"✗ Backtest failed: {results['error']}")
            return
    except Exception as e:
        print(f"✗ Error running backtest: {e}")
        return

    print()

    # Ask user if they want to start live trading
    while True:
        start_live = input("Do you want to start live trading? (y/n): ").lower().strip()
        if start_live in ['y', 'yes']:
            print()
            print("Starting live trading...")
            print("Press Ctrl+C to stop the bot gracefully")
            print()
            try:
                bot.start()
            except KeyboardInterrupt:
                print("\nBot stopped by user")
            break
        elif start_live in ['n', 'no']:
            print("Exiting without starting live trading.")
            break
        else:
            print("Please enter 'y' for yes or 'n' for no.")

if __name__ == "__main__":
    main()
