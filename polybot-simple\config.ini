# PolyBot Simple Configuration File
# Fill in your API keys and adjust trading parameters to your preferences

[API]
# Gemini API credentials for market data
gemini_api_key = 
gemini_api_secret = 

# Polymarket API credentials for trading
polymarket_api_key = 
polymarket_api_secret = 

[TRADING]
# Trading symbol (e.g., BTCUSD, ETHUSD)
symbol = BTCUSD

# Trade size (amount to trade per signal)
trade_size = 0.001

# Moving average windows for strategy
short_ma_window = 10
long_ma_window = 50

# Risk management parameters
stop_loss_pct = 2.0
take_profit_pct = 5.0

[BOT]
# Update interval in seconds (how often to check for signals)
update_interval = 60

# Number of days for backtesting
backtest_days = 30

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
log_level = INFO
